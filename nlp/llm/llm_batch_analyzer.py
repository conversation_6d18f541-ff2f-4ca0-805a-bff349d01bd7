import argparse
import async<PERSON>
from datetime import datetime
import time
from typing import Any, Dict, List, Optional

import httpx
from tqdm import tqdm
from apis.llm.anthropic import AnthropicManager
from apis.llm.base import BaseAPIManager
from apis.llm.data_types import ACTIVE_BATCH_STATUSES, COMPLETED_BATCH_STATUSES, INCOMPLETED_BATCH_STATUSES, BatchStatus, CompletionRequest, CompletionStatus
from apis.llm.openai import OpenAIManager
from db.database import DatabaseManager
from nlp.llm.prompt import PromptManager
from nlp.llm.utils import TZ, LLMConfig, extract_article_id, generate_custom_id, get_target_dates, process_article

# Configure logging
from utils.logging_config import configure_logging
logger = configure_logging(__name__, log_file='llm_analyzer.log')

API_MODELS = {
    'openai': 'gpt-4.1-nano',
    'anthropic': 'claude-3-5-haiku'
}


class LLMBatchAnalyzer:
    """Handles batch processing of articles."""

    def __init__(self, config: LLMConfig, db: DatabaseManager):
        self.config = config
        self.db = db
        self.prompt_manager = PromptManager()
        self.llm_apis = self._initialize_llm_apis()

    def _initialize_llm_apis(self) -> Dict[str, BaseAPIManager]:
        """Initialize the LLM APIs."""
        api_mapping = {
            'openai': OpenAIManager,
            'anthropic': AnthropicManager
        }

        llm_apis = {}
        for api_name, api_class in api_mapping:
            llm_apis[api_name] = api_class(
                requests_per_minute=self.config.requests_per_minute
            )
        return llm_apis

    def save_completion(self, result_dict: Dict[str, Any], api_name: str, prompt_type: str, batch_id: str) -> Optional[Dict[str, Any]]:
        """Save completion result to database."""
        try:
            article_id = extract_article_id(result_dict['custom_id'])
            db_record = {
                "id": result_dict['custom_id'],
                'article_id': article_id,
                "api": api_name,
                "prompt_type": prompt_type,
                "model": result_dict['model'],
                "status": result_dict['status'],
                "batch_id": batch_id,
                "cost": result_dict.get('cost') or result_dict.get('estimated_cost'),
                "raw_response": result_dict.get('raw_response')
            }
            if 'user_prompt' in result_dict and 'system_prompt' in result_dict:
                db_record['prompt'] = result_dict['user_prompt'] + \
                    result_dict['system_prompt']

            if 'content' in result_dict:
                parsed_result = self.prompt_manager.parse_result(
                    result_dict['content'], prompt_type)
                db_record['content'] = parsed_result

                # Save to article metadata
                if self.db.article_service.add_article_metadata(
                        article_id, {prompt_type: db_record['content']}):
                    logger.info(
                        f"Successfully saved in article metadata: {article_id} ")
                else:
                    logger.error(
                        f"Failed to save in article metadata: {article_id}")

            if self.db.llm_api_service.upsert_llm_result(db_record):
                logger.info(
                    f"Successfully saved result: {result_dict['custom_id']}")
            else:
                logger.error(
                    f"Failed to save result: {result_dict['custom_id']}")

            return db_record
        except Exception as e:
            logger.error(f"Error saving result: {e}")

        return None

    def save_batch(self, batch_dict: Dict[str, Any], api_name: str, prompt_type: str) -> Dict[str, Any]:
        """Save batch request information to database."""
        try:
            db_record = {
                'id': batch_dict['id'],
                'api': api_name,
                'status': batch_dict['status'],
                'created_at': batch_dict['created_at'],
                'completed_at': batch_dict.get('completed_at'),
                'expires_at': batch_dict['expires_at'],
                'prompt_type': prompt_type,
                'raw_response': batch_dict['raw_response'],
                'cost': batch_dict.get('cost')
            }
            self.db.llm_api_service.upsert_llm_batch(db_record)

            logger.info(f"Successfully saved batch: {batch_dict['id']}")
            return db_record

        except Exception as e:
            logger.error(f"Error saving batch: {e}")
            return {}

    def create_batch_request(self, api_name: str, prompt_type: str, model: str, target_articles: List[Dict[str, Any]]):
        """Process articles in batch."""
        try:
            llm_api = self.llm_apis[api_name]
            requests = []
            prompt = self.prompt_manager.get_prompt(prompt_type)
            system_prompt = prompt['system_prompt']

            for article in target_articles:
                article_input = process_article(article)
                if not article_input:
                    continue

                rid = generate_custom_id(
                    article['id'], prompt_type, api_name)

                formatted_prompt = prompt['prompt_template'].format(
                    title=article_input['title'],
                    content=article_input['content']
                )

                request = CompletionRequest(
                    max_tokens=self.config.max_tokens,
                    temperature=self.config.temperature,
                    user_prompt=formatted_prompt,
                    system_prompt=system_prompt,
                    model=model,
                    custom_id=rid
                )
                requests.append(request)

            batch = llm_api.get_completion_batch(requests)
            if not batch:
                return None

            # Wait for batch to be validated (openai)
            if batch.status == BatchStatus.VALIDATING.value:
                logger.info(
                    f"Batch {batch.id} is validating. Waiting for completion...")
                while batch and batch.status == BatchStatus.VALIDATING.value:
                    time.sleep(5)
                    batch = llm_api.retrieve_batch(
                        batch.id, fetch_results=False)

            if batch.status == BatchStatus.FAILED.value:
                logger.error(f"Batch {batch.id} failed")
                return None

            self.save_batch(batch.to_dict(), api_name, prompt_type)

            for request in requests:
                request_dict = request.to_dict()
                request_dict['status'] = CompletionStatus.IN_PROGRESS.value
                self.save_completion(
                    request_dict, api_name, prompt_type, batch_id=batch.id)

            return batch

        except Exception as e:
            logger.error(f"Error processing batch: {e}")
            return None

    def get_article_candidates(
        self,
        prompt_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        max_articles: Optional[int] = None,
        filter_target_dates: bool = False
    ) -> List[Dict[str, Any]]:
        # Get date range
        start_dt, end_dt = self.db.article_service.get_article_date_range()
        if start_date:
            start_dt = max(start_dt, datetime.strptime(
                start_date, "%Y-%m-%d").replace(tzinfo=TZ).date())
        if end_date:
            end_dt = min(end_dt, datetime.strptime(
                end_date, "%Y-%m-%d").replace(tzinfo=TZ).date())

        # Get target dates
        target_dates = get_target_dates(
            start_dt, end_dt) if filter_target_dates else None

        if target_dates:
            logger.info(f"Filter articles on {len(target_dates)} target dates")

        articles = self.db.article_service.get_articles_for_llm_batch(
            # Exclude already succeeded articles with the api and prompt type
            prompt_type,
            llm_excluded_statuses=[CompletionStatus.SUCCEEDED.value,
                                   CompletionStatus.IN_PROGRESS.value],
            date_list=target_dates,
            start_date=start_dt,
            end_date=end_dt,
            limit=max_articles,
            # Filter short articles
            min_words=self.config.min_input
        )
        if start_dt and end_dt:
            logger.info(
                f"Fetched {len(articles)} articles between {start_dt.strftime('%Y-%m-%d')} and {end_dt.strftime('%Y-%m-%d')}")
        else:
            logger.info(f"Fetched {len(articles)} articles")

        return articles

    async def retrieve_and_process_batches(self, client: httpx.AsyncClient, prompt_type: str) -> None:
        """Retrieve and process batches."""
        active_batches = self.db.llm_api_service.get_llm_batch_status(
            prompt_type=prompt_type,
            included_status=ACTIVE_BATCH_STATUSES
        )
        logger.info(f"Found {len(active_batches)} active batches")
        for batch_record in active_batches:
            api_name = batch_record['api']
            llm_api = self.llm_apis.get(api_name)
            batch = llm_api.retrieve_batch(
                batch_record['id'], fetch_results=True)
            if not batch:
                logger.error(f"Failed to retrieve batch {batch_record['id']}")
                continue

            logger.info(
                f"Retrieved batch {batch.id} with status {batch.status}")
            if batch.status in COMPLETED_BATCH_STATUSES:
                logger.info(
                    f"Batch {batch.id} is completed. Saving {len(batch.completion_results)} results...")
                for result in batch.completion_results:
                    self.save_completion(
                        result.to_dict(), api_name, prompt_type, batch_id=batch.id)
                batch.status = BatchStatus.PROCESSED.value
            elif batch.status in INCOMPLETED_BATCH_STATUSES:
                logger.error(f"Batch {batch.id} failed. Status {batch.status}")
                requests = self.db.llm_api_service.get_llm_results(
                    batch_id=batch.id)
                logger.info(
                    f"Marking {len(requests)} requests as failed...")
                for request in requests:
                    request['status'] = CompletionStatus.FAILED.value
                    self.save_completion(
                        request, api_name, prompt_type, batch_id=batch.id)
            elif batch.status in ACTIVE_BATCH_STATUSES:
                logger.info(f"Batch {batch.id} is still active")
            else:
                logger.error(f"Invalid batch status {batch.status}")

            self.save_batch(batch.to_dict(), api_name, prompt_type)

    async def analyze_articles_by_batch(
        self,
        client: httpx.AsyncClient,
        prompt_type: str,
        max_articles: Optional[int] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        filter_target_dates: bool = False
    ):
        """Analyze articles using batch processing."""
        # Get articles to process
        articles = self.get_article_candidates(
            prompt_type, start_date, end_date, max_articles, filter_target_dates
        )

        logger.info(
            f"Found {len(articles)} articles to process")

        article_batches = []
        for i in range(0, len(articles), self.config.batch_size):
            article_batches.append(articles[i:i + self.config.batch_size])

        for article_batch in tqdm(article_batches, desc="Processing batches"):
            # Process batch
            batch = None
            while not batch:
                batch = self.create_batch_request(
                    'openai', prompt_type, API_MODELS['openai'], article_batch
                )

                if not batch:
                    batch = self.create_batch_request(
                        'anthropic', prompt_type, API_MODELS['anthropic'], article_batch
                    )

                if not batch:
                    logger.error("Failed to create batch")
                    time.sleep(60)


async def main():
    """Main function to run the analyzer from command line."""
    parser = argparse.ArgumentParser(
        description='Analyze news articles using LLM')

    parser.add_argument('-t', '--prompt-type',
                        default='influence', help='Type of analysis to perform')

    # Date filtering
    parser.add_argument('-s', '--start-date', help='Start date (YYYY-MM-DD)')
    parser.add_argument('-e', '--end-date', help='End date (YYYY-MM-DD)')
    parser.add_argument('--filter-target-dates', action='store_true',
                        help='Filter dates based on SPY price movements')

    # Resource limits
    parser.add_argument('-b', '--budget', type=float, default=0.1,
                        help='Maximum budget for API calls')
    parser.add_argument('--max-input', type=int, default=2048,
                        help='Maximum input words')
    parser.add_argument('--max-tokens', type=int, default=512,
                        help='Maximum output tokens')
    parser.add_argument('--min-input', type=int, default=200,
                        help='Minimum input words')
    parser.add_argument('--requests-per-minute', type=int, default=10,
                        help='Rate limit per minute')

    # Batch processing
    parser.add_argument('--batch-mode', action='store_true',
                        help='Use batch processing')
    parser.add_argument('--max-articles', type=int, default=10_000,
                        help='Maximum number of articles')
    parser.add_argument('--batch-size', type=int, default=20,
                        help='Batch size')
    args = parser.parse_args()

    try:
        llm_config = LLMConfig(
            max_tokens=args.max_tokens,
            max_input=args.max_input,
            min_input=args.min_input,
            temperature=0.7,
            requests_per_minute=args.requests_per_minute
        )

        db_manager = DatabaseManager()
        batch_analyzer = LLMBatchAnalyzer(llm_config, db_manager)

        current_total_cost = db_manager.llm_api_service.get_total_cost(
            prompt_type=args.prompt_type)
        if current_total_cost >= args.budget:
            logger.warning(f"Current cost {current_total_cost} exceeds budget limit "
                           f"{args.budget} for prompt type {args.prompt_type}.")
        BaseAPIManager.set_budget(args.budget, current_total_cost)

        queue = asyncio.Queue()
        async with httpx.AsyncClient() as client:
            await asyncio.gather(
                batch_analyzer.retrieve_and_process_batches(
                    queue, client, args.prompt_type),
                batch_analyzer.analyze_articles_by_batch(, client, prompt_type=args.prompt_type,
                                                         max_articles=args.max_articles,
                                                         start_date=args.start_date,
                                                         end_date=args.end_date,
                                                         filter_target_dates=args.filter_target_dates,)
            )

    except Exception as e:
        logger.error(f"Application error: {e}")


if __name__ == '__main__':
    asyncio.run(main())
