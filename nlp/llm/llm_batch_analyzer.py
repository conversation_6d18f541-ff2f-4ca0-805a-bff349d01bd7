import argparse
import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional
from contextlib import asynccontextmanager

import httpx
from tqdm.asyncio import tqdm
from apis.llm.anthropic import AnthropicManager
from apis.llm.base import BaseAPIManager
from apis.llm.data_types import ACTIVE_BATCH_STATUSES, COMPLETED_BATCH_STATUSES, INCOMPLETED_BATCH_STATUSES, BatchStatus, CompletionRequest, CompletionStatus
from apis.llm.openai import OpenAIManager
from db.database import DatabaseManager
from nlp.llm.prompt import PromptManager
from nlp.llm.utils import TZ, LLMConfig, extract_article_id, generate_custom_id, get_target_dates, process_article

# Configure logging
from utils.logging_config import configure_logging
logger = configure_logging(__name__, log_file='llm_analyzer.log')

API_MODELS = {
    'openai': 'gpt-4.1-nano',
    'anthropic': 'claude-3-5-haiku'
}

# Async configuration constants
DEFAULT_POLLING_INTERVAL = 30  # seconds
MAX_POLLING_INTERVAL = 300  # 5 minutes
POLLING_BACKOFF_FACTOR = 1.5
DEFAULT_BATCH_TIMEOUT = 3600  # 1 hour
MAX_CONCURRENT_BATCHES = 5
MAX_CONCURRENT_POLLS = 10


class AsyncAPIWrapper:
    """Async wrapper for synchronous API managers."""

    def __init__(self, api_manager: BaseAPIManager, client: httpx.AsyncClient):
        self.api_manager = api_manager
        self.client = client

    async def get_completion_batch(self, requests: List[CompletionRequest]) -> Optional[Any]:
        """Async wrapper for batch creation."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.api_manager.get_completion_batch, requests)

    async def retrieve_batch(self, batch_id: str, fetch_results: bool = True) -> Optional[Any]:
        """Async wrapper for batch retrieval."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.api_manager.retrieve_batch, batch_id, fetch_results)

    async def wait_for_validation(self, batch_id: str, timeout: float = DEFAULT_BATCH_TIMEOUT) -> Optional[Any]:
        """Async polling for batch validation with exponential backoff."""
        start_time = asyncio.get_event_loop().time()
        polling_interval = DEFAULT_POLLING_INTERVAL

        while True:
            current_time = asyncio.get_event_loop().time()
            if current_time - start_time > timeout:
                logger.error(
                    f"Timeout waiting for batch {batch_id} validation")
                return None

            batch = await self.retrieve_batch(batch_id, fetch_results=False)
            if not batch:
                logger.error(f"Failed to retrieve batch {batch_id}")
                return None

            if batch.status != BatchStatus.VALIDATING.value:
                return batch

            logger.info(
                f"Batch {batch_id} still validating, waiting {polling_interval}s...")
            await asyncio.sleep(polling_interval)

            # Exponential backoff with max limit
            polling_interval = min(
                polling_interval * POLLING_BACKOFF_FACTOR, MAX_POLLING_INTERVAL)


class LLMBatchAnalyzer:
    """Handles batch processing of articles with async operations."""

    def __init__(self, config: LLMConfig, db: DatabaseManager):
        self.config = config
        self.db = db
        self.prompt_manager = PromptManager()
        self.llm_apis = self._initialize_llm_apis()
        self._async_apis: Dict[str, AsyncAPIWrapper] = {}
        self._semaphore = asyncio.Semaphore(MAX_CONCURRENT_BATCHES)
        self._poll_semaphore = asyncio.Semaphore(MAX_CONCURRENT_POLLS)

    def _initialize_llm_apis(self) -> Dict[str, BaseAPIManager]:
        """Initialize the LLM APIs."""
        api_mapping = {
            'openai': OpenAIManager,
            'anthropic': AnthropicManager
        }

        llm_apis = {}
        for api_name, api_class in api_mapping.items():
            llm_apis[api_name] = api_class(
                requests_per_minute=self.config.requests_per_minute
            )
        return llm_apis

    def _get_async_api(self, api_name: str, client: httpx.AsyncClient) -> AsyncAPIWrapper:
        """Get or create async API wrapper."""
        if api_name not in self._async_apis:
            self._async_apis[api_name] = AsyncAPIWrapper(
                self.llm_apis[api_name], client)
        return self._async_apis[api_name]

    def save_completion(self, result_dict: Dict[str, Any], api_name: str, prompt_type: str, batch_id: str) -> Optional[Dict[str, Any]]:
        """Save completion result to database."""
        try:
            article_id = extract_article_id(result_dict['custom_id'])
            db_record = {
                "id": result_dict['custom_id'],
                'article_id': article_id,
                "api": api_name,
                "prompt_type": prompt_type,
                "model": result_dict['model'],
                "status": result_dict['status'],
                "batch_id": batch_id,
                "cost": result_dict.get('cost') or result_dict.get('estimated_cost'),
                "raw_response": result_dict.get('raw_response')
            }
            if 'user_prompt' in result_dict and 'system_prompt' in result_dict:
                db_record['prompt'] = result_dict['user_prompt'] + \
                    result_dict['system_prompt']

            if 'content' in result_dict:
                parsed_result = self.prompt_manager.parse_result(
                    result_dict['content'], prompt_type)
                db_record['content'] = parsed_result

                # Save to article metadata
                if self.db.article_service.add_article_metadata(
                        article_id, {prompt_type: db_record['content']}):
                    logger.info(
                        f"Successfully saved in article metadata: {article_id} ")
                else:
                    logger.error(
                        f"Failed to save in article metadata: {article_id}")

            if self.db.llm_api_service.upsert_llm_result(db_record):
                logger.info(
                    f"Successfully saved result: {result_dict['custom_id']}")
            else:
                logger.error(
                    f"Failed to save result: {result_dict['custom_id']}")

            return db_record
        except Exception as e:
            logger.error(f"Error saving result: {e}")

        return None

    def save_batch(self, batch_dict: Dict[str, Any], api_name: str, prompt_type: str) -> Dict[str, Any]:
        """Save batch request information to database."""
        try:
            db_record = {
                'id': batch_dict['id'],
                'api': api_name,
                'status': batch_dict['status'],
                'created_at': batch_dict['created_at'],
                'completed_at': batch_dict.get('completed_at'),
                'expires_at': batch_dict['expires_at'],
                'prompt_type': prompt_type,
                'raw_response': batch_dict['raw_response'],
                'cost': batch_dict.get('cost')
            }
            self.db.llm_api_service.upsert_llm_batch(db_record)

            logger.info(f"Successfully saved batch: {batch_dict['id']}")
            return db_record

        except Exception as e:
            logger.error(f"Error saving batch: {e}")
            return {}

    async def create_batch_request(self, client: httpx.AsyncClient, api_name: str, prompt_type: str, model: str, target_articles: List[Dict[str, Any]]):
        """Process articles in batch asynchronously."""
        async with self._semaphore:  # Limit concurrent batch creation
            try:
                async_api = self._get_async_api(api_name, client)
                requests = []
                prompt = self.prompt_manager.get_prompt(prompt_type)
                system_prompt = prompt['system_prompt']

                for article in target_articles:
                    article_input = process_article(article)
                    if not article_input:
                        continue

                    rid = generate_custom_id(
                        article['id'], prompt_type, api_name)

                    formatted_prompt = prompt['prompt_template'].format(
                        title=article_input['title'],
                        content=article_input['content']
                    )

                    request = CompletionRequest(
                        max_tokens=self.config.max_tokens,
                        temperature=self.config.temperature,
                        user_prompt=formatted_prompt,
                        system_prompt=system_prompt,
                        model=model,
                        custom_id=rid
                    )
                    requests.append(request)

                batch = await async_api.get_completion_batch(requests)
                if not batch:
                    return None

                # Wait for batch to be validated (openai) with async polling
                if batch.status == BatchStatus.VALIDATING.value:
                    logger.info(
                        f"Batch {batch.id} is validating. Waiting for completion...")
                    batch = await async_api.wait_for_validation(batch.id)
                    if not batch:
                        return None

                if batch.status == BatchStatus.FAILED.value:
                    logger.error(f"Batch {batch.id} failed")
                    return None

                self.save_batch(batch.to_dict(), api_name, prompt_type)

                for request in requests:
                    request_dict = request.to_dict()
                    request_dict['status'] = CompletionStatus.IN_PROGRESS.value
                    self.save_completion(
                        request_dict, api_name, prompt_type, batch_id=batch.id)

                return batch

            except Exception as e:
                logger.error(f"Error processing batch: {e}")
                return None

    def get_article_candidates(
        self,
        prompt_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        max_articles: Optional[int] = None,
        filter_target_dates: bool = False
    ) -> List[Dict[str, Any]]:
        # Get date range
        start_dt, end_dt = self.db.article_service.get_article_date_range()
        if start_date:
            start_dt = max(start_dt, datetime.strptime(
                start_date, "%Y-%m-%d").replace(tzinfo=TZ).date())
        if end_date:
            end_dt = min(end_dt, datetime.strptime(
                end_date, "%Y-%m-%d").replace(tzinfo=TZ).date())

        # Get target dates
        target_dates = get_target_dates(
            start_dt, end_dt) if filter_target_dates else None

        if target_dates:
            logger.info(f"Filter articles on {len(target_dates)} target dates")

        articles = self.db.article_service.get_articles_for_llm_batch(
            # Exclude already succeeded articles with the api and prompt type
            prompt_type,
            llm_excluded_statuses=[CompletionStatus.SUCCEEDED.value,
                                   CompletionStatus.IN_PROGRESS.value],
            date_list=target_dates,
            start_date=start_dt,
            end_date=end_dt,
            limit=max_articles,
            # Filter short articles
            min_words=self.config.min_input
        )
        if start_dt and end_dt:
            logger.info(
                f"Fetched {len(articles)} articles between {start_dt.strftime('%Y-%m-%d')} and {end_dt.strftime('%Y-%m-%d')}")
        else:
            logger.info(f"Fetched {len(articles)} articles")

        return articles

    async def _process_single_batch(self, client: httpx.AsyncClient, batch_record: Dict[str, Any], prompt_type: str) -> None:
        """Process a single batch asynchronously."""
        async with self._poll_semaphore:  # Limit concurrent polling
            try:
                api_name = batch_record['api']
                async_api = self._get_async_api(api_name, client)

                batch = await async_api.retrieve_batch(batch_record['id'], fetch_results=True)
                if not batch:
                    logger.error(
                        f"Failed to retrieve batch {batch_record['id']}")
                    return

                logger.info(
                    f"Retrieved batch {batch.id} with status {batch.status}")

                if batch.status in COMPLETED_BATCH_STATUSES:
                    logger.info(
                        f"Batch {batch.id} is completed. Saving {len(batch.completion_results)} results...")
                    for result in batch.completion_results:
                        self.save_completion(
                            result.to_dict(), api_name, prompt_type, batch_id=batch.id)
                    batch.status = BatchStatus.PROCESSED.value

                elif batch.status in INCOMPLETED_BATCH_STATUSES:
                    logger.error(
                        f"Batch {batch.id} failed. Status {batch.status}")
                    requests = self.db.llm_api_service.get_llm_results(
                        batch_id=batch.id)
                    logger.info(
                        f"Marking {len(requests)} requests as failed...")
                    for request in requests:
                        request['status'] = CompletionStatus.FAILED.value
                        self.save_completion(
                            request, api_name, prompt_type, batch_id=batch.id)

                elif batch.status in ACTIVE_BATCH_STATUSES:
                    logger.info(f"Batch {batch.id} is still active")
                else:
                    logger.error(f"Invalid batch status {batch.status}")

                self.save_batch(batch.to_dict(), api_name, prompt_type)

            except Exception as e:
                logger.error(
                    f"Error processing batch {batch_record.get('id', 'unknown')}: {e}")

    async def retrieve_and_process_batches(self, client: httpx.AsyncClient, prompt_type: str) -> None:
        """Retrieve and process batches concurrently."""
        active_batches = self.db.llm_api_service.get_llm_batch_status(
            prompt_type=prompt_type,
            included_status=ACTIVE_BATCH_STATUSES
        )
        logger.info(f"Found {len(active_batches)} active batches")

        if not active_batches:
            return

        # Process batches concurrently
        tasks = [
            self._process_single_batch(client, batch_record, prompt_type)
            for batch_record in active_batches
        ]

        await asyncio.gather(*tasks, return_exceptions=True)

    async def _create_batch_with_fallback(self, client: httpx.AsyncClient, prompt_type: str, article_batch: List[Dict[str, Any]]) -> Optional[Any]:
        """Create batch with API fallback and retry logic."""
        max_retries = 3
        retry_delay = 60  # seconds

        for attempt in range(max_retries):
            try:
                # Try OpenAI first
                batch = await self.create_batch_request(
                    client, 'openai', prompt_type, API_MODELS['openai'], article_batch
                )
                if batch:
                    return batch

                # Fallback to Anthropic
                batch = await self.create_batch_request(
                    client, 'anthropic', prompt_type, API_MODELS['anthropic'], article_batch
                )
                if batch:
                    return batch

                logger.warning(
                    f"Failed to create batch (attempt {attempt + 1}/{max_retries})")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)

            except Exception as e:
                logger.error(
                    f"Error creating batch (attempt {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)

        logger.error("Failed to create batch after all retries")
        return None

    async def analyze_articles_by_batch(
        self,
        client: httpx.AsyncClient,
        prompt_type: str,
        max_articles: Optional[int] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        filter_target_dates: bool = False
    ):
        """Analyze articles using async batch processing."""
        # Get articles to process
        articles = self.get_article_candidates(
            prompt_type, start_date, end_date, max_articles, filter_target_dates
        )

        logger.info(f"Found {len(articles)} articles to process")

        if not articles:
            logger.info("No articles to process")
            return

        # Split articles into batches
        article_batches = []
        for i in range(0, len(articles), self.config.batch_size):
            article_batches.append(articles[i:i + self.config.batch_size])

        # Process batches concurrently with semaphore control
        async def process_batch(article_batch):
            return await self._create_batch_with_fallback(client, prompt_type, article_batch)

        # Use tqdm for async progress tracking
        tasks = [process_batch(batch) for batch in article_batches]

        logger.info(
            f"Processing {len(article_batches)} batches concurrently...")
        results = []

        # Process with progress bar
        for task in tqdm(asyncio.as_completed(tasks), total=len(tasks), desc="Creating batches"):
            try:
                result = await task
                results.append(result)
            except Exception as e:
                logger.error(f"Error processing batch: {e}")
                results.append(None)

        successful_batches = [r for r in results if r is not None]
        logger.info(
            f"Successfully created {len(successful_batches)}/{len(article_batches)} batches")

    async def get_batch_status_summary(self, prompt_type: str) -> Dict[str, int]:
        """Get a summary of batch statuses for monitoring."""
        try:
            all_batches = self.db.llm_api_service.get_llm_batch_status(
                prompt_type=prompt_type)
            status_counts = {}

            for batch_record in all_batches:
                status = batch_record.get('status', 'unknown')
                status_counts[status] = status_counts.get(status, 0) + 1

            return status_counts
        except Exception as e:
            logger.error(f"Error getting batch status summary: {e}")
            return {}

    async def monitor_batch_progress(self, prompt_type: str, interval: int = 300):
        """Monitor batch progress with periodic status updates."""
        logger.info(
            f"Starting batch monitoring for {prompt_type} (interval: {interval}s)")

        while True:
            try:
                status_summary = await self.get_batch_status_summary(prompt_type)
                if status_summary:
                    logger.info(f"Batch status summary: {status_summary}")

                    # Check if all batches are completed or failed
                    active_count = sum(status_summary.get(status, 0)
                                       for status in ACTIVE_BATCH_STATUSES)
                    if active_count == 0:
                        logger.info(
                            "No active batches remaining, stopping monitoring")
                        break

                await asyncio.sleep(interval)

            except Exception as e:
                logger.error(f"Error during batch monitoring: {e}")
                await asyncio.sleep(interval)

    @asynccontextmanager
    async def async_context(self):
        """Async context manager for proper resource cleanup."""
        try:
            yield self
        finally:
            # Cleanup async resources
            self._async_apis.clear()
            logger.info("Async resources cleaned up")


async def main():
    """Main function to run the analyzer from command line."""
    parser = argparse.ArgumentParser(
        description='Analyze news articles using LLM')

    parser.add_argument('-t', '--prompt-type',
                        default='influence', help='Type of analysis to perform')

    # Date filtering
    parser.add_argument('-s', '--start-date', help='Start date (YYYY-MM-DD)')
    parser.add_argument('-e', '--end-date', help='End date (YYYY-MM-DD)')
    parser.add_argument('--filter-target-dates', action='store_true',
                        help='Filter dates based on SPY price movements')

    # Resource limits
    parser.add_argument('-b', '--budget', type=float, default=0.1,
                        help='Maximum budget for API calls')
    parser.add_argument('--max-input', type=int, default=2048,
                        help='Maximum input words')
    parser.add_argument('--max-tokens', type=int, default=512,
                        help='Maximum output tokens')
    parser.add_argument('--min-input', type=int, default=200,
                        help='Minimum input words')
    parser.add_argument('--requests-per-minute', type=int, default=10,
                        help='Rate limit per minute')

    # Batch processing
    parser.add_argument('--batch-mode', action='store_true',
                        help='Use batch processing')
    parser.add_argument('--max-articles', type=int, default=10_000,
                        help='Maximum number of articles')
    parser.add_argument('--batch-size', type=int, default=20,
                        help='Batch size')

    # Async configuration
    parser.add_argument('--monitor-only', action='store_true',
                        help='Only monitor existing batches, do not create new ones')
    parser.add_argument('--monitor-interval', type=int, default=300,
                        help='Monitoring interval in seconds')
    parser.add_argument('--max-concurrent-batches', type=int, default=MAX_CONCURRENT_BATCHES,
                        help='Maximum concurrent batch operations')
    parser.add_argument('--max-concurrent-polls', type=int, default=MAX_CONCURRENT_POLLS,
                        help='Maximum concurrent polling operations')

    args = parser.parse_args()

    try:
        llm_config = LLMConfig(
            max_tokens=args.max_tokens,
            max_input=args.max_input,
            min_input=args.min_input,
            temperature=0.7,
            requests_per_minute=args.requests_per_minute
        )

        db_manager = DatabaseManager()
        batch_analyzer = LLMBatchAnalyzer(llm_config, db_manager)

        # Update concurrency limits from command line args
        batch_analyzer._semaphore = asyncio.Semaphore(
            args.max_concurrent_batches)
        batch_analyzer._poll_semaphore = asyncio.Semaphore(
            args.max_concurrent_polls)

        current_total_cost = db_manager.llm_api_service.get_total_cost(
            prompt_type=args.prompt_type)
        if current_total_cost >= args.budget:
            logger.warning(f"Current cost {current_total_cost} exceeds budget limit "
                           f"{args.budget} for prompt type {args.prompt_type}.")
        BaseAPIManager.set_budget(args.budget, current_total_cost)

        # Configure HTTP client with appropriate timeouts
        timeout = httpx.Timeout(
            connect=30.0,  # Connection timeout
            read=300.0,    # Read timeout for large responses
            write=30.0,    # Write timeout
            pool=60.0      # Pool timeout
        )

        async with httpx.AsyncClient(timeout=timeout) as client:
            async with batch_analyzer.async_context():
                try:
                    if args.monitor_only:
                        # Only monitor existing batches
                        logger.info("Running in monitor-only mode")
                        await batch_analyzer.monitor_batch_progress(
                            args.prompt_type,
                            interval=args.monitor_interval
                        )
                    else:
                        # Run both operations concurrently with timeout
                        tasks = [
                            batch_analyzer.retrieve_and_process_batches(
                                client, args.prompt_type),
                            batch_analyzer.analyze_articles_by_batch(
                                client,
                                prompt_type=args.prompt_type,
                                max_articles=args.max_articles,
                                start_date=args.start_date,
                                end_date=args.end_date,
                                filter_target_dates=args.filter_target_dates
                            )
                        ]

                        # Add monitoring task if requested
                        if args.monitor_interval > 0:
                            tasks.append(
                                batch_analyzer.monitor_batch_progress(
                                    args.prompt_type,
                                    interval=args.monitor_interval
                                )
                            )

                        await asyncio.wait_for(
                            asyncio.gather(*tasks, return_exceptions=True),
                            timeout=DEFAULT_BATCH_TIMEOUT * 2  # Overall timeout
                        )

                except asyncio.TimeoutError:
                    logger.error("Operation timed out")
                except Exception as e:
                    logger.error(
                        f"Unexpected error during batch processing: {e}")
                    raise

    except Exception as e:
        logger.error(f"Application error: {e}")


if __name__ == '__main__':
    asyncio.run(main())
